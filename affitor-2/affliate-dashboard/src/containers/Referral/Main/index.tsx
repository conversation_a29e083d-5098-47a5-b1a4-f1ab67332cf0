import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Loader2, AlertCircle } from "lucide-react";
import Overview from "./Overview";
import Steps from "./Steps";

interface MainContainerProps {
  onJoinClick: () => void;
  isLoading?: boolean;
  error?: string | null;
}

const MainContainer: React.FC<MainContainerProps> = ({
  onJoinClick,
  isLoading = false,
  error = null,
}) => {
  // Sample FAQ data
  const faqItems = [
    {
      question: "How do I get started with the affiliate program?",
      answer:
        "Simply sign up through our affiliate portal, get approved, and start sharing your unique referral links.",
    },
    {
      question: "When and how will I get paid?",
      answer:
        "Payments are processed monthly. You can choose to receive your commission via bank transfer, PayPal, or crypto.",
    },
    {
      question: "How much can I earn?",
      answer:
        "You can earn up to 50% commission on every successful referral, depending on your performance and tier level.",
    },
    {
      question: "Is there a minimum payout threshold?",
      answer:
        "Yes, the minimum payout amount is $50. Once you reach this amount, you can request a payout.",
    },
    {
      question: "Do referrals expire?",
      answer:
        "No, your referrals don't expire. You'll earn commission on all purchases made by users you've referred, even if they make their first purchase months after clicking your link.",
    },
    {
      question: "What are the advertising restrictions",
      answer:
        "You are not allowed to run keyword advertising on Google for promotional purposes.",
    },
  ];

  return (
    <div className="max-w-[1200px] mx-auto py-16 px-4 md:px-6 space-y-20">
      {/* Overview Section */}
      <Overview onJoinClick={onJoinClick} isLoading={isLoading} error={error} />

      {/* Main Title */}
      <section className="bg-primary rounded-xl p-10 md:p-16 text-center shadow-lg border border-primary/20 hover:shadow-xl transition-all">
        <h2 className="text-3xl md:text-5xl font-bold text-primary-foreground">
          Earn up to $588 Commission from Your Every Referral
        </h2>
        <div className="w-24 h-1 bg-primary-foreground/30 mx-auto mt-6 rounded-full"></div>
      </section>

      {/* Steps Process */}
      <Steps />

      {/* Example Section */}
      <section className="pt-10">
        <h2 className="text-2xl md:text-3xl font-bold text-center mb-12 relative">
          <span className="relative z-10">Earning Potential</span>
          <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 h-3 w-24 bg-primary/20 -z-0 rounded-full"></span>
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {/* Basic Tier */}
          <Card className="border-t-4 border-t-blue-400 shadow-md hover:shadow-lg transition-shadow overflow-hidden group">
            <CardHeader className="bg-blue-50 dark:bg-blue-950/30 transition-colors">
              <CardTitle className="text-center text-blue-600 dark:text-blue-400">
                Basic
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center pt-6">
              <h3 className="text-4xl font-bold mb-2 text-blue-600 dark:text-blue-400">
                25%
              </h3>
              <p className="text-muted-foreground">Commission Rate</p>
              <div className="my-6 border-t" />
              <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg transform group-hover:-translate-y-1 transition-transform">
                <p>
                  Example: 5 referrals × $98 ={" "}
                  <span className="font-bold text-blue-600 dark:text-blue-400">
                    $122.50
                  </span>{" "}
                  earnings
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Pro Tier */}
          <Card className="border-t-4 border-t-purple-400 shadow-md hover:shadow-lg transition-shadow overflow-hidden group">
            <CardHeader className="bg-purple-50 dark:bg-purple-950/30 transition-colors">
              <CardTitle className="text-center text-purple-600 dark:text-purple-400">
                Pro
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center pt-6">
              <h3 className="text-4xl font-bold mb-2 text-purple-600 dark:text-purple-400">
                35%
              </h3>
              <p className="text-muted-foreground">Commission Rate</p>
              <div className="my-6 border-t" />
              <div className="p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg transform group-hover:-translate-y-1 transition-transform">
                <p>
                  Example: 8 referrals × $98 ={" "}
                  <span className="font-bold text-purple-600 dark:text-purple-400">
                    $274.60
                  </span>{" "}
                  earnings
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Premium Tier */}
          <Card className="border-t-4 border-t-green-400 shadow-md hover:shadow-lg transition-shadow overflow-hidden group">
            <CardHeader className="bg-green-50 dark:bg-green-950/30 transition-colors">
              <CardTitle className="text-center text-green-600 dark:text-green-400">
                Premium
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center pt-6">
              <h3 className="text-4xl font-bold mb-2 text-green-600 dark:text-green-400">
                50%
              </h3>
              <p className="text-muted-foreground">Commission Rate</p>
              <div className="my-6 border-t" />
              <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg transform group-hover:-translate-y-1 transition-transform">
                <p>
                  Example: 12 referrals × $98 ={" "}
                  <span className="font-bold text-green-600 dark:text-green-400">
                    $588
                  </span>{" "}
                  earnings
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="pt-10">
        <h2 className="text-2xl md:text-3xl font-bold text-center mb-12 relative">
          <span className="relative z-10">Frequently Asked Questions</span>
          <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 h-3 w-24 bg-primary/20 -z-0 rounded-full"></span>
        </h2>
        <div className="max-w-3xl mx-auto">
          <Accordion type="single" collapsible className="w-full">
            {faqItems.map((item, index) => (
              <AccordionItem
                key={index}
                value={`item-${index}`}
                className="border border-muted rounded-lg mb-4 px-2 overflow-hidden shadow-sm hover:shadow-md transition-shadow"
              >
                <AccordionTrigger className="hover:no-underline text-left py-4">
                  <span className="font-medium">{item.question}</span>
                </AccordionTrigger>
                <AccordionContent className="pb-4 text-muted-foreground">
                  <p>{item.answer}</p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </section>

      {/* Footer CTA */}
      <section className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-10 md:p-16 text-center shadow-md transform hover:shadow-lg transition-all">
        <h2 className="text-2xl md:text-4xl font-bold mb-8 text-foreground">
          Start Earning with Our Affiliate Program Now
        </h2>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center justify-center">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        )}

        <Button
          size="lg"
          className="px-8 py-6 text-lg rounded-full shadow-md hover:shadow-lg transition-shadow min-w-[200px]"
          onClick={onJoinClick}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              Joining Program...
            </>
          ) : (
            "Join Our Affiliate Program"
          )}
        </Button>
      </section>
    </div>
  );
};

export default MainContainer;
