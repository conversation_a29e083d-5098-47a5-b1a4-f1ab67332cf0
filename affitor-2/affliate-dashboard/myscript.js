const axios = require('axios');

const API_BASE = 'http://prod-alb-521497661.us-east-1.elb.amazonaws.com/content-manager/collection-types/api::user-tracking-request.user-tracking-request';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzUzNTMyNTI0LCJleHAiOjE3NTYxMjQ1MjR9.58D99-lOaezol5Ci0HpgXaVJm0_OUhoEfkAp2lWi9ZY';
const COMMON_HEADERS = {
  'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
  'Cache-Control': 'no-cache',
  'Connection': 'keep-alive',
  'Pragma': 'no-cache',
  'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
  'accept': 'application/json',
  'authorization': AUTH_TOKEN,
  'content-type': 'application/json',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"macOS"',
  'Cookie': 'referral_code=2222; referral_url=https://affitor.com/ai-agent1?via=2222; referral_short_link=hello',
};

async function processBatches() {
  let page = 1;
  const pageSize = 100;
  let totalPages = 1;
  let totalProcessed = 0;
  console.log('Starting to fetch and process items batch by batch...');
  do {
    console.log(`Fetching page ${page}...`);
    const url = `${API_BASE}?page=${page}&pageSize=${pageSize}&sort=id%3AASC&filters%5B%24and%5D%5B0%5D%5Brequest_limit%5D%5B%24eq%5D=200`;
    const res = await axios.get(url, { headers: COMMON_HEADERS });
    const data = res.data;
    const items = data.results || data.data || [];
    console.log(`Fetched ${items.length} items from page ${page}. Processing batch...`);
    await Promise.all(items.map(async (item, idx) => {
      const id = item.documentId;
      try {
        await updateItem(id);
        totalProcessed++;
        console.log(`[${totalProcessed}] Updated item ${id}`);
      } catch (err) {
        console.error(`Error updating item ${id}:`, err.message);
      }
    }));
    // Pagination info
    if (data.pagination) {
      totalPages = data.pagination.pageCount;
      console.log(`Total pages: ${totalPages}`);
    } else {
      // fallback: stop if less than pageSize returned
      totalPages = items.length < pageSize ? page : page + 1;
    }
    page++;
  } while (page <= totalPages);
  console.log('All batches processed.');
}

async function updateItem(id) {
  const url = `${API_BASE}/${id}/actions/publish?`;
  const data = {
    request_count: 0,
    request_limit: 25,
    last_request_date: new Date().toISOString(),
    statistics: {},
    users_permissions_user: { connect: [], disconnect: [] },
    subscription_tier: { connect: [], disconnect: [] },
    transaction: { connect: [], disconnect: [] },
  };
  const headers = {
    ...COMMON_HEADERS,
    Referer: `http://prod-alb-521497661.us-east-1.elb.amazonaws.com/admin/content-manager/collection-types/api::user-tracking-request.user-tracking-request/${id}`,
    Origin: 'http://prod-alb-521497661.us-east-1.elb.amazonaws.com',
  };
  console.log(`Updating item with documentId: ${id} ...`);
  try {
    const res = await axios.post(url, data, { headers });
    console.log(`Successfully updated item ${id}`);
    return res.data;
  } catch (err) {
    if (err.response) {
      console.error(`Failed to update ${id}: ${err.response.status} ${JSON.stringify(err.response.data)}`);
      throw new Error(`Failed to update ${id}: ${err.response.status} ${JSON.stringify(err.response.data)}`);
    } else {
      console.error(`Failed to update ${id}: ${err.message}`);
      throw new Error(`Failed to update ${id}: ${err.message}`);
    }
  }
}

async function main() {
  try {
    console.log('Script started.');
    await processBatches();
    console.log('Script finished.');
  } catch (err) {
    console.error('Script failed:', err.message);
  }
}

main();
